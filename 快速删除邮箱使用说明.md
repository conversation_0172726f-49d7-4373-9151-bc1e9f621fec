# 快速删除邮箱工具使用说明

## 功能特点

这是一个专门用于快速删除iCloud隐藏邮箱地址的工具，具有以下特点：

### 🚀 高速删除
- **简化逻辑**：专注于删除操作，去除了复杂的数据库操作
- **多线程并发**：支持多个线程同时删除，大幅提升速度
- **随机选择**：每次随机选择邮箱进行删除，避免重复操作

### 🔧 技术特点
- **端口管理**：从1501端口开始，每个线程使用独立端口
- **共享登录**：多个窗口使用同一个账号登录，减少登录次数
- **智能重试**：遇到错误自动重试，提高成功率
- **实时监控**：UI界面实时显示每个线程的删除进度

## 使用方法

### 1. 配置账号信息
在桌面或程序目录下创建 `.env` 文件，格式如下：
```env
# 账号1
ACCOUNT_1_EMAIL=<EMAIL>
ACCOUNT_1_PASSWORD=your_password
ACCOUNT_1_API_URL=http://your_api_url

# 账号2（可选）
ACCOUNT_2_EMAIL=<EMAIL>
ACCOUNT_2_PASSWORD=your_password2
ACCOUNT_2_API_URL=http://your_api_url2

# 总账号数量
TOTAL_ACCOUNTS=2
```

### 2. 启动程序
```bash
python fast_delete_emails.py
```

### 3. 检查配置状态
- 程序会自动加载 `.env` 文件中的账号配置
- 界面会显示已加载的账号数量和API状态
- 确认配置正确后再开始删除

### 4. 设置线程数量
- 程序会根据可用账号数量自动设置推荐线程数
- 可以手动调整，但不超过可用账号数量
- 多个线程会循环使用配置的账号

### 5. 开始删除
- 点击"开始快速删除"按钮
- 程序会自动打开多个浏览器窗口
- 每个窗口使用不同账号独立进行删除操作

### 6. 监控进度
- **线程状态表格**：显示每个线程的运行状态和删除数量
- **运行日志**：显示详细的操作日志，包括账号信息
- **实时更新**：删除数量会实时更新

## 操作流程

程序的自动化操作流程如下：

1. **配置加载**
   - 自动从桌面或程序目录加载 `.env` 配置文件
   - 解析账号信息（邮箱、密码、接码API）
   - 验证配置有效性

2. **浏览器初始化**
   - 使用独立端口启动Chrome浏览器（从1501开始）
   - 导航到Apple账户隐私页面

3. **自动登录**
   - 检测是否需要登录
   - 自动输入配置的邮箱和密码
   - 智能处理验证码（通过接码API自动获取）
   - 完成登录流程

4. **删除循环**
   - 点击"隐藏邮件地址"卡片
   - 随机选择一个邮箱
   - 点击删除或停用按钮
   - 确认删除/停用操作
   - 关闭对话框，继续下一轮

5. **多线程协作**
   - 多个线程同时工作，提升删除效率
   - 每个线程使用不同账号，避免冲突
   - 智能重试机制，确保稳定运行

## 注意事项

### ⚠️ 重要提醒
- **不可逆操作**：删除的邮箱无法恢复，请谨慎使用
- **网络稳定**：确保网络连接稳定，避免操作中断
- **配置文件**：确保 `.env` 文件格式正确，包含完整的账号信息
- **接码API**：确保接码API地址有效，能正常获取验证码

### 🔒 安全建议
- 使用前请确认要删除所有隐藏邮箱
- 建议先备份重要的邮箱地址
- 不要在删除过程中手动操作浏览器窗口
- 保护好 `.env` 文件，避免账号信息泄露

### 💡 性能优化
- **线程数量**：程序会根据可用账号数量自动优化
- **账号分配**：多线程会循环使用配置的账号，避免冲突
- **内存使用**：每个线程会占用一定内存，注意监控
- **CPU负载**：多线程会增加CPU使用率

## 故障排除

### 常见问题

1. **配置加载失败**
   - 检查 `.env` 文件是否存在于桌面或程序目录
   - 确认文件格式正确，每行格式为 `KEY=VALUE`
   - 检查账号信息是否完整（邮箱、密码、API都必须配置）

2. **登录失败**
   - 检查 `.env` 文件中的邮箱和密码是否正确
   - 确认接码API地址有效
   - 查看日志中的具体错误信息

3. **验证码处理失败**
   - 检查接码API是否正常工作
   - 确认API返回格式正确
   - 手动测试API地址是否可访问

4. **找不到邮箱卡片**
   - 可能所有邮箱已被删除
   - 检查网络连接是否正常
   - 尝试手动刷新页面

5. **线程卡住**
   - 点击"停止删除"按钮
   - 重新启动程序
   - 减少线程数量重试

6. **删除失败**
   - 检查页面是否正常加载
   - 确认账号权限正常
   - 尝试单线程模式

### 日志分析
- 查看运行日志中的错误信息
- 注意线程状态的变化
- 关注删除数量的增长情况

## 技术说明

### 依赖库
- `DrissionPage`：用于浏览器自动化
- `tkinter`：用于GUI界面
- `threading`：用于多线程处理
- `requests`：用于接码API调用
- `re`：用于验证码正则提取

### 配置管理
- 使用 `ConfigManager` 类统一管理配置
- 支持从桌面或程序目录加载 `.env` 文件
- 自动验证配置完整性和有效性

### 端口分配
- 线程1：端口1501
- 线程2：端口1502
- 线程3：端口1503
- 以此类推...

### 元素定位
程序使用CSS选择器和XPath定位页面元素：
- 隐藏邮件地址卡片：`css:h3.card-title`
- 邮箱卡片：`css:div.card-line`
- 删除按钮：`tag:button@text():删除地址`
- 停用按钮：`tag:button@text():停用电子邮件地址`
- 验证码输入框：`css:.form-security-code-input`

### 接码API集成
- 自动调用配置的接码API获取验证码
- 使用正则表达式提取6位数字验证码
- 支持多种API响应格式

## 版本信息

- **版本**：1.0
- **作者**：快速删除工具
- **更新日期**：2025年
- **兼容性**：Windows/macOS/Linux

## 免责声明

本工具仅供学习和研究使用，使用者需要：
- 自行承担使用风险
- 遵守相关法律法规
- 尊重Apple服务条款
- 谨慎操作，避免误删重要数据
