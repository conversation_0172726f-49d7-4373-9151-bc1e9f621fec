"""
智能管理器
功能：智能决策逻辑，通过页面元素判断账号状态，自动选择最优操作策略
"""

import time
import threading
from typing import Dict, Optional
from enum import Enum


from database import DatabaseManager
from element_provider import AuthManager, EmailCreator, EmailDeleter


class OperationStrategy(Enum):
    """操作策略枚举"""
    DIRECT_CREATE = "direct_create"                    # 直接创建
    DELETE_USED_THEN_CREATE = "delete_used_then_create"  # 删除已使用→创建
    DELETE_CURRENT_THEN_CREATE = "delete_current_then_create"  # 停用删除当前→创建


class SmartManager:
    """智能管理器"""
    
    def __init__(self, thread_id: int, account_info: Dict[str, str]):
        self.thread_id = thread_id
        self.account_info = account_info
        
        # 线程控制
        self.should_stop = threading.Event()
        self.is_paused = threading.Event()
        self.is_paused.set()  # 默认运行状态
        
        # 统计信息
        self.operation_count = 0
        self.create_count = 0
        self.delete_count = 0
        
        # 管理器组件
        self.db_manager = DatabaseManager()
        self.auth_manager = None
        self.email_creator = None
        self.email_deleter = None
        
        print(f"线程{self.thread_id}: 初始化智能管理器 - {account_info['email']}")

    def check_account_full_status(self) -> bool:
        """检查账号是否已满"""
        try:
            # 使用AuthManager的元素获取方法
            full_indicator = self.auth_manager.get_account_full_indicator_element()
            if full_indicator:
                # 检查是否包含 "创建新地址" 的隐藏文本
                hidden_span = full_indicator.ele('css:span.visuallyhidden', timeout=1)
                if hidden_span and "创建新地址" in hidden_span.text:
                    return True
            return False
        except Exception as e:
            print(f"线程{self.thread_id}: 检测账号已满状态时出错: {e}")
            return False

    def navigate_to_target(self) -> bool:
        """导航到目标页面并确保登录"""
        try:
            # 确保已登录
            if not self.ensure_logged_in():
                return False

            # 导航到目标页面
            current_url = self.auth_manager.tab.url
            if not current_url.startswith(self.auth_manager.TARGET_URL):
                print(f"线程{self.thread_id}: 导航到目标页面...")
                self.auth_manager.tab.get(self.auth_manager.TARGET_URL)
                time.sleep(1)

            return True

        except Exception as e:
            print(f"线程{self.thread_id}: 导航到目标页面时出错: {e}")
            return False

    def ensure_logged_in(self) -> bool:
        """确保已登录状态"""
        try:
            # 首先检查当前状态
            state = self.detect_current_page_state()

            if state == "logged_in":
                return True

            # 如果未登录，导航到目标页面（会自动跳转到登录页面）
            print(f"线程{self.thread_id}: 未登录，导航到目标页面...")
            self.auth_manager.tab.get(self.auth_manager.TARGET_URL)
            time.sleep(1)

            # 执行登录流程
            return self.handle_login_flow()

        except Exception as e:
            print(f"线程{self.thread_id}: 确保登录状态时出错: {e}")
            return False

    def detect_current_page_state(self) -> str:
        """检测当前页面状态"""
        try:
            current_url = self.auth_manager.tab.url
            print(f"线程{self.thread_id}: 当前页面URL: {current_url}")

            # 检查是否在验证码页面
            verification_input = self.auth_manager.get_verification_input_element()
            if verification_input:
                return "verification_code"

            # 检查是否已登录到目标页面
            if current_url.startswith(self.auth_manager.TARGET_URL):
                return "logged_in"

            # 检查是否在登录页面
            if current_url.startswith(self.auth_manager.LOGIN_URL):
                return "login_page"

            # 其他情况需要跳转到目标页面
            print(f"线程{self.thread_id}: 检测到错误页面，立即跳转到目标页面")
            self.auth_manager.tab.get(self.auth_manager.TARGET_URL)
            time.sleep(1)
            # 重新检测页面状态
            return self.detect_current_page_state()

        except Exception as e:
            print(f"线程{self.thread_id}: 检测页面状态时出错: {e}")
            return "unknown"



    def handle_login_flow(self) -> bool:
        """处理登录流程"""
        try:
            credentials = self.auth_manager.get_account_credentials()
            if not all([credentials['email'], credentials['password'], credentials['api_url']]):
                print(f"线程{self.thread_id}: 账号信息不完整，无法登录")
                return False

            print(f"线程{self.thread_id}: 开始登录流程...")

            # 最多重试5次
            for retry in range(1, 6):
                print(f"线程{self.thread_id}: 第{retry}次尝试登录...")

                # 检测当前页面状态
                state = self.detect_current_page_state()
                print(f"线程{self.thread_id}: 检测到页面状态: {state}")

                if state == "logged_in":
                    print(f"线程{self.thread_id}: 已登录成功！")
                    return True

                elif state == "verification_code":
                    print(f"线程{self.thread_id}: 检测到验证码页面，开始处理验证码...")
                    if self.handle_verification_code():
                        continue  # 重新检测状态
                    else:
                        print(f"线程{self.thread_id}: 验证码处理失败，刷新页面重试...")
                        self.auth_manager.tab.refresh()
                        time.sleep(1)
                        continue

                elif state == "login_page":
                    print(f"线程{self.thread_id}: 检测到登录页面，执行登录操作...")
                    if self.execute_login_steps():
                        time.sleep(1)  # 等待页面跳转
                        continue  # 重新检测状态
                    else:
                        print(f"线程{self.thread_id}: 登录操作失败")

                elif state == "unknown":
                    print(f"线程{self.thread_id}: 未知页面状态，刷新页面...")
                    self.auth_manager.tab.refresh()
                    time.sleep(1)
                    continue

                # 如果到这里说明当前尝试失败，等待后重试
                if retry < 5:
                    print(f"线程{self.thread_id}: 第{retry}次尝试失败，2秒后重试...")
                    time.sleep(2)

            print(f"线程{self.thread_id}: 5次尝试全部失败，登录失败")
            return False

        except Exception as e:
            print(f"线程{self.thread_id}: 登录流程出错: {e}")
            return False

    def execute_login_steps(self) -> bool:
        """执行登录步骤"""
        try:
            credentials = self.auth_manager.get_account_credentials()

            # 1. 输入邮箱
            email_input = self.auth_manager.get_email_input_element(3)  # 3秒超时
            if email_input:
                try:
                    current_value = email_input.value or ""
                    if not current_value:
                        email_input.clear()
                        email_input.input(credentials['email'])
                        print(f"线程{self.thread_id}: 已输入邮箱")
                        time.sleep(0.5)

                        # 输入邮箱后点击登录按钮
                        login_button = self.auth_manager.get_login_button_element(3)  # 3秒超时
                        if login_button:
                            login_button.click()
                            print(f"线程{self.thread_id}: 已点击登录按钮（邮箱步骤）")
                            time.sleep(1)
                except Exception as e:
                    print(f"线程{self.thread_id}: 输入邮箱失败: {e}")

                # 2. 等待并输入密码
                # 使用超时等待密码输入框出现
                password_input = self.auth_manager.get_password_input_element(timeout=5)

                if password_input:
                    try:
                        current_value = password_input.value or ""
                        if not current_value:
                            password_input.clear()
                            password_input.input(credentials['password'])
                            print(f"线程{self.thread_id}: 已输入密码")
                            time.sleep(0.5)

                            # 输入密码后点击登录按钮
                            login_button = self.auth_manager.get_login_button_element(3)  # 3秒超时
                            if login_button:
                                login_button.click()
                                print(f"线程{self.thread_id}: 已点击登录按钮（密码步骤）")
                                time.sleep(1)
                                return True
                    except Exception as e:
                        print(f"线程{self.thread_id}: 输入密码失败: {e}")
                else:
                    print(f"线程{self.thread_id}: 密码输入框未出现，登录失败")

            return False
        except Exception as e:
            print(f"线程{self.thread_id}: 执行登录步骤出错: {e}")
            return False

    def handle_verification_code(self) -> bool:
        """处理验证码"""
        try:
            code_input = self.auth_manager.get_verification_input_element()
            if not code_input:
                return False

            print(f"线程{self.thread_id}: 开始处理验证码，最多尝试3次...")

            for attempt in range(1, 4):
                show_details = (attempt == 1 or attempt == 3)
                if show_details:
                    print(f"线程{self.thread_id}: 第{attempt}次尝试获取验证码...")

                verification_code = self.get_verification_code_from_api(show_details=show_details)
                if verification_code:
                    code_input.input(verification_code)
                    print(f"线程{self.thread_id}: 第{attempt}次成功获取并输入验证码: {verification_code}")
                    time.sleep(1)

                    # 检查是否还在验证码页面
                    if not self.auth_manager.get_verification_input_element():
                        print(f"线程{self.thread_id}: 验证码验证成功，已跳转")
                        return self.verify_login_success_by_url()

                if attempt < 3:
                    if show_details:
                        print(f"线程{self.thread_id}: 第{attempt}次尝试失败，1秒后重试...")
                    time.sleep(0.5)
                else:
                    print(f"线程{self.thread_id}: 3次尝试全部失败")

            return False
        except Exception as e:
            print(f"线程{self.thread_id}: 处理验证码出错: {e}")
            return False

    def get_verification_code_from_api(self, show_details: bool = False) -> Optional[str]:
        """从API获取验证码"""
        try:
            import requests
            import json
            import re
            credentials = self.auth_manager.get_account_credentials()

            response = requests.get(credentials['api_url'], timeout=3)
            if response.status_code == 200:
                response_text = response.text.strip()

                if show_details:
                    print(f"线程{self.thread_id}: API原始响应: {response_text}")

                # 尝试解析JSON格式
                try:
                    json_data = json.loads(response_text)

                    if json_data.get('code') == 1 and 'data' in json_data:
                        # 有验证码的情况
                        code_text = json_data['data'].get('code', '')
                        if show_details:
                            print(f"线程{self.thread_id}: 解析到code字段: {code_text}")

                        # 从文本中提取6位数字验证码
                        code_match = re.search(r'(\d{6})', code_text)
                        if code_match:
                            verification_code = code_match.group(1)
                            if show_details:
                                print(f"线程{self.thread_id}: 提取到验证码: {verification_code}")
                            return verification_code
                        else:
                            if show_details:
                                print(f"线程{self.thread_id}: 未在文本中找到6位数字")
                    elif json_data.get('code') == 0:
                        # 无验证码的情况
                        if show_details:
                            msg = json_data.get('msg', 'Unknown')
                            print(f"线程{self.thread_id}: API返回无验证码状态: {msg}")
                    else:
                        if show_details:
                            print(f"线程{self.thread_id}: 未知的API响应格式")
                except json.JSONDecodeError:
                    # 如果不是JSON，尝试直接提取数字
                    if show_details:
                        print(f"线程{self.thread_id}: 不是JSON格式，尝试直接提取数字")

                    code_match = re.search(r'(\d{6})', response_text)
                    if code_match:
                        verification_code = code_match.group(1)
                        if show_details:
                            print(f"线程{self.thread_id}: 直接提取到验证码: {verification_code}")
                        return verification_code

            if show_details:
                print(f"线程{self.thread_id}: API未返回有效验证码")
            return None

        except Exception as e:
            if show_details:
                print(f"线程{self.thread_id}: 获取验证码API出错: {e}")
            return None

    def verify_login_success_by_url(self) -> bool:
        """通过URL验证登录是否成功"""
        try:
            print(f"线程{self.thread_id}: 开始验证登录状态...")

            # 等待页面跳转完成，最多等待10秒
            for wait_time in range(1, 11):
                time.sleep(1)
                current_url = self.auth_manager.tab.url

                if current_url == self.auth_manager.TARGET_URL:
                    print(f"线程{self.thread_id}: ✅ 登录成功！已到达目标页面")
                    return True

                # 显示等待进度
                if wait_time in [3, 6, 9]:
                    print(f"线程{self.thread_id}: 等待页面跳转中... ({wait_time}/10秒)")

            # 10秒后仍未到达目标页面
            final_url = self.auth_manager.tab.url
            print(f"线程{self.thread_id}: ❌ 登录可能失败，未到达目标页面")
            print(f"线程{self.thread_id}: 当前URL: {final_url}")
            print(f"线程{self.thread_id}: 期望URL: {self.auth_manager.TARGET_URL}")
            return False

        except Exception as e:
            print(f"线程{self.thread_id}: URL验证过程出错: {e}")
            return False

    def initialize_components(self) -> bool:
        """初始化所有组件"""
        try:
            # 初始化认证管理器
            self.auth_manager = AuthManager(self.thread_id, self.account_info)
            self.auth_manager.open_browser(self.auth_manager.TARGET_URL)
            
            # 初始化邮箱操作组件
            self.email_creator = EmailCreator(self.thread_id, self.auth_manager, self.db_manager)
            self.email_deleter = EmailDeleter(self.thread_id, self.auth_manager, self.db_manager)
            
            print(f"线程{self.thread_id}: 组件初始化成功")
            return True
            
        except Exception as e:
            print(f"线程{self.thread_id}: 组件初始化失败: {e}")
            return False
    
    def analyze_account_status(self) -> Dict[str, any]:
        """
        分析当前账号状态
        通过页面元素判断，不依赖750数量
        """
        try:
            # 确保在正确页面
            if not self.navigate_to_target():
                return {"error": "无法导航到目标页面"}

            # 点击隐藏邮件地址卡片
            hide_card = self.email_creator.get_hide_email_card_element()
            if hide_card:
                hide_card.click()
                time.sleep(1)
            else:
                return {"error": "无法点击隐藏邮件地址卡片"}

            # 检查账号是否已满 - 使用精确的已满标志检测
            is_full = self.check_account_full_status()
            print(f"线程{self.thread_id}: 已满状态检测完成: {is_full}")
            
            # 获取数据库统计
            available_count, used_count = self.db_manager.get_email_counts()
            print(f"线程{self.thread_id}: 数据库统计完成 - 可用:{available_count}, 已使用:{used_count}")

            # 如果账号未满，不需要获取详细的网页信息
            if not is_full:
                status = {
                    "is_full": False,
                    "visible_count": 0,
                    "available_count": available_count,
                    "used_count": used_count,
                    "deletable_count": 0
                }
                print(f"线程{self.thread_id}: 账号未满，快速返回状态")
                return status

            # 账号已满时才获取详细的网页信息
            print(f"线程{self.thread_id}: 账号已满，获取网页信息...")
            visible_emails = self.get_all_visible_emails()
            visible_count = len(visible_emails)
            print(f"线程{self.thread_id}: 网页邮箱统计完成: {visible_count}")

            # 快速估算可删除邮箱数量（避免逐个验证）
            deletable_count = min(used_count, visible_count) if visible_count > 0 else 0
            
            status = {
                "is_full": is_full,
                "visible_count": visible_count,
                "available_count": available_count,
                "used_count": used_count,
                "deletable_count": deletable_count
            }

            print(f"线程{self.thread_id}: 账号已满状态分析完成 - 可见:{visible_count}, 可删除:{deletable_count}")

            return status
            
        except Exception as e:
            print(f"线程{self.thread_id}: 分析账号状态时出错: {e}")
            return {"error": str(e)}
    
    def determine_operation_strategy(self) -> Dict[str, any]:
        """
        智能决策当前操作策略
        """
        try:
            # 分析账号状态
            status = self.analyze_account_status()
            
            if "error" in status:
                return {"strategy": None, "error": status["error"]}
            
            # 决策逻辑
            if not status["is_full"]:
                # 情况1: 账号未满，直接创建
                print(f"线程{self.thread_id}: 情况1: 账号未满，直接创建")
                return {
                    "strategy": OperationStrategy.DIRECT_CREATE,
                    "priority": "high",
                    "operations": ["create"],
                    "description": f"账号未满，直接创建邮箱",
                    "reason": "账号有空余名额"
                }
            
            elif status["is_full"] and status["deletable_count"] > 0:
                # 情况2: 账号已满但有可删除邮箱
                print(f"线程{self.thread_id}: 情况2: 账号已满，有可删除邮箱")
                return {
                    "strategy": OperationStrategy.DELETE_USED_THEN_CREATE,
                    "priority": "medium",
                    "operations": ["delete_used", "create"],
                    "description": f"账号已满，有{status['deletable_count']}个可删除邮箱，删除后创建",
                    "reason": "优先删除已使用邮箱释放名额"
                }
            
            elif status["is_full"] and status["visible_count"] > 0:
                # 情况3: 账号已满且无可删除邮箱，但有可见邮箱
                print(f"线程{self.thread_id}: 情况3: 账号已满，无可删除邮箱，有可见邮箱")
                return {
                    "strategy": OperationStrategy.DELETE_CURRENT_THEN_CREATE,
                    "priority": "low",
                    "operations": ["delete_current", "create"],
                    "description": f"账号已满且无已使用邮箱，停用删除当前邮箱后创建",
                    "reason": "无已使用邮箱，只能删除当前邮箱"
                }
            
            else:
                # 情况4: 无法操作
                print(f"线程{self.thread_id}: 情况4: 无法操作")
                return {
                    "strategy": None,
                    "priority": "none",
                    "operations": [],
                    "description": "账号已满且无可操作邮箱",
                    "reason": "需要手动处理"
                }
                
        except Exception as e:
            print(f"线程{self.thread_id}: 决策操作策略时出错: {e}")
            return {"strategy": None, "error": str(e)}

    # ========== 新的阶段式流程方法 ==========

    def ensure_in_management_page(self) -> bool:
        """阶段1: 导航阶段 - 确保进入隐藏邮件管理页面"""
        try:
            # 检查是否已经在管理页面
            close_btn = self.email_creator.tab.ele("css:span.icon.icon-close", timeout=1)
            if close_btn and close_btn.states.is_displayed:
                print(f"线程{self.thread_id}: 已在管理页面")
                return True

            # 如果不在管理页面，尝试进入
            print(f"线程{self.thread_id}: 不在管理页面，尝试进入...")

            # 确保在正确的主页面
            if not self.navigate_to_target():
                return False

            # 查找并点击隐藏邮件地址卡片（入口）
            hide_card = self.email_creator.get_hide_email_card_element(2)  # 2秒超时
            if hide_card:
                hide_card.click()
                print(f"线程{self.thread_id}: 成功点击隐藏邮件地址卡片入口")
                time.sleep(2)  # 等待页面加载

                # 验证是否成功进入管理页面
                close_btn = self.email_creator.get_close_button_element(3)  # 3秒超时
                if close_btn and close_btn.states.is_displayed:
                    print(f"线程{self.thread_id}: 成功进入管理页面")
                    return True
                else:
                    print(f"线程{self.thread_id}: 点击后未进入管理页面")
                    return False
            else:
                print(f"线程{self.thread_id}: 未找到隐藏邮件地址卡片入口")
                return False

        except Exception as e:
            print(f"线程{self.thread_id}: 导航到管理页面时出错: {e}")
            return False

    def detect_account_route(self) -> str:
        """阶段2: 状态判断阶段 - 检测账号状态并决定路线"""
        try:
            print(f"线程{self.thread_id}: 检测账号状态...")

            # 检测创建按钮（账号未满的标志）
            create_btn = self.email_creator.get_create_button_element()
            if create_btn:
                print(f"线程{self.thread_id}: 检测到创建按钮 - 账号未满")
                return "创建路线"

            # 检测邮箱卡片（账号已满的标志）
            email_cards = self.email_deleter.get_email_cards_elements()
            if email_cards and len(email_cards) > 0:
                print(f"线程{self.thread_id}: 检测到 {len(email_cards)} 个邮箱卡片 - 账号已满")
                return "删除路线"

            # 都没检测到，可能页面还在加载
            print(f"线程{self.thread_id}: 未检测到明确状态，页面可能在加载中")
            return None

        except Exception as e:
            print(f"线程{self.thread_id}: 检测账号路线时出错: {e}")
            return None

    def execute_single_operation(self, operation: str) -> bool:
        """执行单个操作"""
        try:
            if operation == "create":
                success = self.handle_create_dialog()
                if success:
                    self.create_count += 1
                return success

            elif operation == "delete_used":
                success = self.handle_delete_used_flow()
                if success:
                    self.delete_count += 1
                return success

            elif operation == "delete_current":
                success = self.handle_delete_current_flow()
                if success:
                    self.delete_count += 1
                return success

            return False

        except Exception as e:
            print(f"线程{self.thread_id}: 执行操作 {operation} 时出错: {e}")
            return False

    def execute_create_route(self) -> bool:
        """阶段3A: 创建路线 - 执行创建邮箱的操作序列"""
        try:
            print(f"线程{self.thread_id}: 开始执行创建路线...")

            # 创建操作序列: 创建按钮 → 输入框 → 提交按钮 → 获取结果 → 关闭
            while not self.should_stop.is_set():
                # 1. 查找创建按钮
                create_btn = self.email_creator.get_create_button_element(2)  # 2秒超时
                if create_btn:
                    create_btn.click()
                    print(f"线程{self.thread_id}: 点击创建按钮")
                    time.sleep(1)
                    continue

                # 3. 查找邮箱输入框
                email_input = self.email_creator.get_email_prefix_input_element(3)  # 3秒超时
                if email_input:
                    import random
                    import string
                    prefix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
                    email_input.clear()
                    email_input.input(prefix)
                    print(f"线程{self.thread_id}: 输入邮箱前缀: {prefix}")

                # 4. 查找提交按钮
                submit_btn = self.email_creator.get_submit_button_element(3)  # 3秒超时
                if submit_btn:
                    submit_btn.click()
                    print(f"线程{self.thread_id}: 点击提交按钮")

                # 5. 查找创建成功的邮箱
                created_email_element = self.email_creator.get_created_email_element(5)  # 5秒超时
                if created_email_element:
                    created_email = created_email_element.text.strip()
                    print(f"线程{self.thread_id}: 成功创建邮箱: {created_email}")

                    # 保存到数据库
                    if self.db_manager.save_email(created_email):
                        print(f"线程{self.thread_id}: 邮箱已保存到数据库")
                        self.create_count += 1
                    else:
                        print(f"线程{self.thread_id}: 邮箱保存到数据库失败")

                    # 继续查找关闭按钮
                    # continue

                # 6. 查找关闭按钮（完成创建流程）
                close_btn = self.email_creator.get_close_button_element(5)  # 3秒超时
                if close_btn:
                    close_btn.click()
                    print(f"线程{self.thread_id}: 点击关闭按钮，创建流程完成")
                    time.sleep(1)
                    return True

                # 如果没有找到任何元素，短暂等待
                print(f"线程{self.thread_id}: 创建路线中，等待元素出现...")

        except Exception as e:
            print(f"线程{self.thread_id}: 执行创建路线时出错: {e}")
            return False

    def execute_delete_route(self) -> bool:
        """阶段3B: 删除路线 - 仅删除已使用的邮箱"""
        try:
            print(f"线程{self.thread_id}: 开始执行删除路线...")

            # 1. 查询数据库获取已使用邮箱列表
            used_emails = self.db_manager.get_used_emails()
            if not used_emails or len(used_emails) == 0:
                print(f"线程{self.thread_id}: 数据库中无已使用邮箱，退出删除路线")
                return False

            # 2. 选择第一个已使用邮箱作为删除目标
            target_email = used_emails[0]
            print(f"线程{self.thread_id}: 选择删除目标: {target_email}")

            # 3. 在页面查找目标邮箱卡片
            target_card = self.find_target_email_card(target_email)
            if not target_card:
                print(f"线程{self.thread_id}: 未在页面找到目标邮箱卡片: {target_email}")
                return False

            # 4. 点击目标邮箱卡片
            target_card.click()
            print(f"线程{self.thread_id}: 点击目标邮箱卡片: {target_email}")
            # time.sleep(3)  # 等待删除选项出现

            # 5. 执行删除操作序列
            while not self.should_stop.is_set():
                # 邮箱卡片已在上面点击过，直接查找删除/停用按钮

                # 2. 查找删除按钮
                delete_btn = self.email_deleter.get_delete_button_element(2)  # 2秒超时
                if delete_btn:
                    delete_btn.click()
                    print(f"线程{self.thread_id}: 点击删除按钮")

                # 3. 查找停用按钮
                deactivate_btn = self.email_deleter.get_deactivate_button_element(2)  # 2秒超时
                if deactivate_btn:
                    deactivate_btn.click()
                    print(f"线程{self.thread_id}: 点击停用按钮")

                # 6. 查找关闭按钮（完成删除流程）
                close_btn = self.email_creator.get_close_button_element(3)  # 3秒超时
                if close_btn:
                    close_btn.click()
                    print(f"线程{self.thread_id}: 点击关闭按钮，删除流程完成")
                    time.sleep(1)
                    return True

                # 4. 查找确认删除按钮
                confirm_delete_btn = self.email_deleter.get_confirm_delete_button_element(2)  # 2秒超时
                if confirm_delete_btn:
                    confirm_delete_btn.click()
                    print(f"线程{self.thread_id}: 确认删除操作")

                    # 从数据库删除该邮箱记录
                    if self.db_manager.delete_email(target_email):
                        print(f"线程{self.thread_id}: 从数据库删除: {target_email}")
                        self.delete_count += 1
                    else:
                        print(f"线程{self.thread_id}: 数据库删除失败: {target_email}")

                # 5. 查找确认停用按钮
                confirm_deactivate_btn = self.email_deleter.get_confirm_deactivate_button_element(2)  # 2秒超时
                if confirm_deactivate_btn:
                    confirm_deactivate_btn.click()
                    print(f"线程{self.thread_id}: 确认停用操作")

                    # 停用操作不从数据库删除，只更新计数
                    self.delete_count += 1
                    print(f"线程{self.thread_id}: 停用完成，邮箱仍保留在数据库: {target_email}")

                # 如果没有找到任何元素，短暂等待
                print(f"线程{self.thread_id}: 删除路线中，等待元素出现...")
                time.sleep(0.5)

        except Exception as e:
            print(f"线程{self.thread_id}: 执行删除路线时出错: {e}")
            return False

    def cache_all_email_cards(self):
        """一次性缓存页面上所有邮箱卡片的信息"""
        try:
            # 获取所有邮箱卡片元素
            email_cards = self.email_deleter.get_email_cards_elements(5)  # 5秒超时
            if not email_cards:
                print(f"线程{self.thread_id}: 页面上未找到任何邮箱卡片")
                return {}

            print(f"线程{self.thread_id}: 开始缓存 {len(email_cards)} 个邮箱卡片...")

            # 创建邮箱地址到元素的映射
            email_card_cache = {}

            for i, card in enumerate(email_cards):
                try:
                    # 获取卡片文本，格式: <div class="card-line"><EMAIL></div>
                    card_text = card.text.strip()
                    if card_text and '@' in card_text:
                        # 提取邮箱地址（去除可能的额外文本）
                        email_address = card_text
                        email_card_cache[email_address] = card
                        print(f"线程{self.thread_id}: 缓存邮箱卡片 {i+1}: {email_address}")
                    else:
                        print(f"线程{self.thread_id}: 第{i+1}个卡片文本格式异常: '{card_text}'")

                except Exception as e:
                    print(f"线程{self.thread_id}: 缓存第{i+1}个卡片时出错: {e}")
                    continue

            print(f"线程{self.thread_id}: 邮箱卡片缓存完成，共缓存 {len(email_card_cache)} 个")
            return email_card_cache

        except Exception as e:
            print(f"线程{self.thread_id}: 缓存邮箱卡片时出错: {e}")
            return {}

    def find_target_email_card(self, target_email: str):
        """在缓存中查找指定邮箱的卡片元素"""
        try:
            # 一次性缓存所有邮箱卡片
            email_card_cache = self.cache_all_email_cards()

            if not email_card_cache:
                print(f"线程{self.thread_id}: 邮箱卡片缓存为空")
                return None

            print(f"线程{self.thread_id}: 在缓存中查找目标邮箱: {target_email}")

            # 精确匹配
            if target_email in email_card_cache:
                print(f"线程{self.thread_id}: 精确匹配找到目标邮箱: {target_email}")
                return email_card_cache[target_email]

            # 模糊匹配（包含关系）
            for cached_email, card_element in email_card_cache.items():
                if target_email in cached_email:
                    print(f"线程{self.thread_id}: 模糊匹配找到目标邮箱: {cached_email} (目标: {target_email})")
                    return card_element

            print(f"线程{self.thread_id}: 在缓存中未找到目标邮箱: {target_email}")
            print(f"线程{self.thread_id}: 缓存中的邮箱列表: {list(email_card_cache.keys())}")
            return None

        except Exception as e:
            print(f"线程{self.thread_id}: 查找目标邮箱卡片时出错: {e}")
            return None

    # ========== 流程控制方法 ==========

    def handle_create_dialog(self) -> bool:
        """处理创建邮箱对话框流程"""
        try:
            print(f"线程{self.thread_id}: 开始创建邮箱流程...")

            # 1. 确保在正确页面
            if not self.navigate_to_target():
                print(f"线程{self.thread_id}: 导航到目标页面失败")
                return False

            # 2. 点击隐藏邮件地址卡片
            hide_card = self.email_creator.get_hide_email_card_element()
            if hide_card:
                hide_card.click()
                print(f"线程{self.thread_id}: 成功点击隐藏邮件地址卡片")
                time.sleep(1)
            else:
                print(f"线程{self.thread_id}: 未找到隐藏邮件地址卡片")
                return False

            # 3. 点击创建按钮
            create_btn = self.email_creator.get_create_button_element()
            if create_btn:
                create_btn.click()
                print(f"线程{self.thread_id}: 成功点击创建按钮")
                time.sleep(1)
            else:
                print(f"线程{self.thread_id}: 未找到创建按钮")
                return False

            # 4. 输入邮箱前缀
            email_input = self.email_creator.get_email_input_element()
            if email_input:
                import random
                import string
                prefix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
                email_input.input(prefix)
                print(f"线程{self.thread_id}: 成功输入邮箱前缀: {prefix}")
                time.sleep(0.5)
            else:
                print(f"线程{self.thread_id}: 未找到邮箱输入框")
                return False

            # 5. 点击提交按钮
            submit_btn = self.email_creator.get_submit_button_element()
            if submit_btn:
                submit_btn.click()
                print(f"线程{self.thread_id}: 成功点击提交按钮")
                time.sleep(1.5)
            else:
                print(f"线程{self.thread_id}: 未找到提交按钮")
                return False

            # 6. 获取创建的邮箱地址
            # created_email_element = self.email_creator.get_created_email_element()
            # if created_email_element:
            #     created_email = created_email_element.text.strip()
            #     print(f"线程{self.thread_id}: 成功创建邮箱: {created_email}")

                # # 7. 保存到数据库
                # if self.db_manager.save_email(created_email):
                #     print(f"线程{self.thread_id}: 邮箱已保存到数据库")
                # else:
                #     print(f"线程{self.thread_id}: 保存到数据库失败")

                # 8. 处理后续操作（关闭对话框等）
            return self.handle_post_operation()
            # else:
            #     print(f"线程{self.thread_id}: 未找到创建的邮箱元素")
            #     return False

        except Exception as e:
            print(f"线程{self.thread_id}: 创建邮箱流程出错: {e}")
            return False

    def handle_delete_used_flow(self) -> bool:
        """处理删除已使用邮箱的流程"""
        try:
            print(f"线程{self.thread_id}: 开始删除已使用邮箱流程...")

            # 1. 确保在正确页面
            if not self.navigate_to_target():
                print(f"线程{self.thread_id}: 导航到目标页面失败")
                return False

            # 2. 点击隐藏邮件地址卡片
            hide_card = self.email_deleter.get_hide_email_card_element()
            if hide_card:
                hide_card.click()
                print(f"线程{self.thread_id}: 成功点击隐藏邮件地址卡片")
                time.sleep(1)
            else:
                print(f"线程{self.thread_id}: 未找到隐藏邮件地址卡片")
                return False

            # 3. 获取最佳删除目标
            target_email = self.get_best_deletion_target()
            if not target_email:
                print(f"线程{self.thread_id}: 未找到可删除的邮箱")
                return False

            print(f"线程{self.thread_id}: 准备删除邮箱: {target_email}")

            # 4. 找到并点击目标邮箱
            email_cards = self.email_deleter.get_email_cards_elements()
            target_card = None
            for card in email_cards:
                if target_email in card.text:
                    target_card = card
                    break

            if target_card:
                target_card.click()
                print(f"线程{self.thread_id}: 成功点击目标邮箱")
                time.sleep(1)
            else:
                print(f"线程{self.thread_id}: 未找到目标邮箱卡片")
                return False

            # 5. 执行删除操作
            return self.delete_email_handler(target_email)

        except Exception as e:
            print(f"线程{self.thread_id}: 删除已使用邮箱流程出错: {e}")
            return False

    def handle_delete_current_flow(self) -> bool:
        """处理删除当前邮箱的流程"""
        try:
            print(f"线程{self.thread_id}: 开始删除当前邮箱流程...")

            # 1. 确保在正确页面
            if not self.navigate_to_target():
                return False

            # 2. 点击隐藏邮件地址卡片
            hide_card = self.email_deleter.get_hide_email_card_element()
            if hide_card:
                hide_card.click()
                time.sleep(1)
            else:
                return False

            # 3. 获取当前显示的邮箱
            current_email = self.get_current_displayed_email()
            if not current_email:
                print(f"线程{self.thread_id}: 未获取到当前邮箱地址")
                return False

            print(f"线程{self.thread_id}: 准备删除当前邮箱: {current_email}")

            # 4. 执行删除操作
            return self.delete_email_handler(current_email)

        except Exception as e:
            print(f"线程{self.thread_id}: 删除当前邮箱流程出错: {e}")
            return False

    def delete_email_handler(self, email_address: str) -> bool:
        """删除邮箱处理器"""
        try:
            print(f"线程{self.thread_id}: 开始删除操作: {email_address}")

            # 1. 尝试删除按钮
            delete_btn = self.email_deleter.get_delete_button_element()
            if delete_btn:
                print(f"线程{self.thread_id}: 找到删除按钮")
                delete_btn.click()
                print(f"线程{self.thread_id}: 点击删除按钮")
                time.sleep(0.5)

                # 查找确认删除按钮
                confirm_btn = self.email_deleter.get_confirm_delete_button_element()
                if confirm_btn:
                    confirm_btn.click()
                    print(f"线程{self.thread_id}: 确认删除操作")
                    time.sleep(1)
                else:
                    print(f"线程{self.thread_id}: 未找到确认删除按钮")
                    return False
            else:
                print(f"线程{self.thread_id}: 未找到删除按钮，尝试停用")
                # 尝试停用按钮
                deactivate_btn = self.email_deleter.get_deactivate_button_element()
                if deactivate_btn:
                    print(f"线程{self.thread_id}: 找到停用按钮")
                    deactivate_btn.click()
                    print(f"线程{self.thread_id}: 点击停用按钮")
                    time.sleep(0.5)

                    # 尝试查找确认停用按钮（可能不存在）
                    time.sleep(0.5)  # 等待可能的对话框出现
                    confirm_btn = self.email_deleter.get_confirm_deactivate_button_element()
                    if confirm_btn:
                        confirm_btn.click()
                        print(f"线程{self.thread_id}: 确认停用操作")
                        time.sleep(1)
                    else:
                        print(f"线程{self.thread_id}: 未找到确认停用按钮，可能已直接执行停用")
                        time.sleep(1)  # 等待操作完成
                else:
                    print(f"线程{self.thread_id}: 未找到删除或停用按钮")
                    return False

            # 2. 处理后续操作
            success = self.handle_post_operation()

            # 3. 更新数据库
            if success:
                if self.db_manager.delete_email(email_address):
                    print(f"线程{self.thread_id}: 数据库更新成功")
                else:
                    print(f"线程{self.thread_id}: 数据库更新失败")

            return success

        except Exception as e:
            print(f"线程{self.thread_id}: 删除邮箱处理器出错: {e}")
            return False

    def handle_post_operation(self) -> bool:
        """处理操作后的通用步骤（如关闭对话框）"""
        try:
            close_btn = self.email_deleter.get_close_button_element()
            if close_btn:
                close_btn.click()
                print(f"线程{self.thread_id}: 成功点击关闭按钮")
                time.sleep(1)
            else:
                print(f"线程{self.thread_id}: 未找到关闭按钮，可能已自动关闭")
            return True

        except Exception as e:
            print(f"线程{self.thread_id}: 处理后续操作时出错: {e}")
            return False

    def get_best_deletion_target(self) -> Optional[str]:
        """获取最佳删除目标邮箱"""
        try:
            # 优先选择数据库中最早的已使用邮箱
            used_emails = self.db_manager.get_used_emails()

            for email in used_emails:
                if self.email_exists_in_webpage(email):
                    print(f"线程{self.thread_id}: 选择已使用邮箱作为删除目标: {email}")
                    return email

            # 如果数据库中的已使用邮箱在网页中都不存在
            # 则选择网页中第一个可见的邮箱
            webpage_emails = self.get_all_visible_emails()
            if webpage_emails:
                print(f"线程{self.thread_id}: 选择网页中第一个邮箱作为删除目标: {webpage_emails[0]}")
                return webpage_emails[0]

            print(f"线程{self.thread_id}: 未找到可删除的邮箱")
            return None

        except Exception as e:
            print(f"线程{self.thread_id}: 获取删除目标时出错: {e}")
            return None

    def email_exists_in_webpage(self, email: str) -> bool:
        """检查邮箱是否在网页中存在"""
        try:
            visible_emails = self.get_all_visible_emails()
            return email in visible_emails
        except:
            return False

    def get_all_visible_emails(self) -> list:
        """获取网页中所有可见的邮箱"""
        try:
            email_cards = self.email_deleter.get_email_cards_elements()
            emails = []
            for card in email_cards:
                # 从卡片文本中提取邮箱地址
                text = card.text
                if '@' in text:
                    # 简单的邮箱提取逻辑
                    lines = text.split('\n')
                    for line in lines:
                        if '@' in line and '.' in line:
                            emails.append(line.strip())
                            break
            return emails
        except Exception as e:
            print(f"线程{self.thread_id}: 获取可见邮箱时出错: {e}")
            return []

    def get_current_displayed_email(self) -> Optional[str]:
        """获取当前显示的邮箱地址"""
        try:
            visible_emails = self.get_all_visible_emails()
            return visible_emails[0] if visible_emails else None
        except Exception as e:
            print(f"线程{self.thread_id}: 获取当前显示邮箱时出错: {e}")
            return None

    def execute_smart_cycle(self) -> None:
        """
        执行智能循环
        """
        try:
            # 初始化组件
            if not self.initialize_components():
                print(f"线程{self.thread_id}: 初始化失败，退出")
                return
            
            cycle_count = 0
            
            while not self.should_stop.is_set():
                # 检查暂停状态
                if not self.is_paused.is_set():
                    print(f"线程{self.thread_id}: 已暂停，等待恢复...")
                    self.is_paused.wait()
                    if self.should_stop.is_set():
                        break
                
                cycle_count += 1
                print(f"\n线程{self.thread_id}: === 第{cycle_count}轮 ===")

                try:
                    # 阶段1: 登录检查
                    page_state = self.detect_current_page_state()
                    if page_state != "logged_in":
                        print(f"线程{self.thread_id}: 需要登录，执行登录流程...")
                        if not self.handle_login_flow():
                            print(f"线程{self.thread_id}: 登录失败，等待重试")
                            time.sleep(3)
                            continue

                    # 阶段2: 导航阶段 - 确保进入管理页面
                    if not self.ensure_in_management_page():
                        print(f"线程{self.thread_id}: 无法进入管理页面，等待重试")
                        if self.should_stop.wait(timeout=2):
                            break
                        continue

                    # 阶段3: 状态判断阶段 - 检测账号状态并选择路线
                    route = self.detect_account_route()
                    if not route:
                        print(f"线程{self.thread_id}: 无法确定操作路线，等待重试")
                        if self.should_stop.wait(timeout=2):
                            break
                        continue

                    print(f"线程{self.thread_id}: 选择路线: {route}")

                    # 阶段4: 操作执行阶段 - 执行对应路线的操作
                    if route == "创建路线":
                        success = self.execute_create_route()
                    elif route == "删除路线":
                        success = self.execute_delete_route()
                    else:
                        print(f"线程{self.thread_id}: 未知路线: {route}")
                        success = False

                    if success:
                        print(f"线程{self.thread_id}: 路线执行成功")
                        self.operation_count += 1
                    else:
                        print(f"线程{self.thread_id}: 路线执行失败")

                    # 短暂等待后继续下一轮
                    if self.should_stop.wait(timeout=1):
                        print(f"线程{self.thread_id}: 在等待间隔时收到停止信号")
                        break
                    
                except Exception as e:
                    print(f"线程{self.thread_id}: 智能循环异常: {e}")
                    if self.should_stop.wait(timeout=2):
                        print(f"线程{self.thread_id}: 在异常等待时收到停止信号")
                        break
            
            print(f"线程{self.thread_id}: 智能循环结束")
            print(f"线程{self.thread_id}: 总操作次数: {self.operation_count}")
            print(f"线程{self.thread_id}: 创建次数: {self.create_count}")
            print(f"线程{self.thread_id}: 删除次数: {self.delete_count}")
            
        except Exception as e:
            print(f"线程{self.thread_id}: 智能循环出错: {e}")
        
        finally:
            if self.auth_manager:
                try:
                    self.auth_manager.tab.quit()
                except:
                    pass
    
    def pause(self):
        """暂停智能循环"""
        self.is_paused.clear()
    
    def resume(self):
        """恢复智能循环"""
        self.is_paused.set()
    
    def stop(self):
        """停止智能循环"""
        self.should_stop.set()
        self.is_paused.set()



