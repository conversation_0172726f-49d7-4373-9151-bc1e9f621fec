"""
邮件管理器 - 主程序
重构版本：智能化一键运行，自动决策操作策略
"""

import time
import threading
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
from enum import Enum
from typing import Dict

from config import ConfigManager
from smart_manager import SmartManager


class OperationType(Enum):
    """操作类型枚举"""
    SMART = "智能运行"
    STOP = "停止"


class SmartWorkerThread:
    """智能工作线程类"""

    def __init__(self, thread_id: int, account_info: Dict[str, str], ui_callback=None, status_callback=None):
        self.thread_id = thread_id
        self.account_info = account_info
        self.ui_callback = ui_callback
        self.status_callback = status_callback

        # 智能管理器
        self.smart_manager = SmartManager(thread_id, account_info)

        print(f"线程{self.thread_id}: 初始化智能工作线程 - {account_info['email']}")

    def run(self):
        """主运行循环"""
        try:
            if self.status_callback:
                self.status_callback(self.thread_id, "正在初始化")

            # 执行智能循环
            self.smart_manager.execute_smart_cycle()

        except Exception as e:
            print(f"线程{self.thread_id}: 智能循环出错: {e}")
            if self.status_callback:
                self.status_callback(self.thread_id, "运行异常")

        finally:
            if self.status_callback:
                self.status_callback(self.thread_id, "已停止")

    def pause(self):
        """暂停线程"""
        self.smart_manager.pause()
        if self.status_callback:
            self.status_callback(self.thread_id, "已暂停")

    def resume(self):
        """恢复线程"""
        self.smart_manager.resume()
        if self.status_callback:
            self.status_callback(self.thread_id, "正常运行")

    def stop(self):
        """停止线程"""
        self.smart_manager.stop()
        if self.status_callback:
            self.status_callback(self.thread_id, "正在停止")

    def get_statistics(self) -> Dict[str, int]:
        """获取统计信息"""
        return {
            "operation_count": self.smart_manager.operation_count,
            "create_count": self.smart_manager.create_count,
            "delete_count": self.smart_manager.delete_count
        }


class EmailManagerUI:
    """智能化邮件管理器UI界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("邮件管理器 - 智能版")
        self.root.geometry("800x600")

        # 配置管理器
        self.config_manager = ConfigManager()

        # 验证配置
        is_valid, errors = self.config_manager.validate_config()
        if not is_valid:
            messagebox.showerror("配置错误", "\n".join(errors))
            self.root.destroy()
            return

        # 线程管理
        self.worker_threads: Dict[int, SmartWorkerThread] = {}
        self.thread_objects: Dict[int, threading.Thread] = {}
        self.is_running = False

        self.setup_ui()
        self.update_status_display()

        # 定时更新统计信息
        self.update_statistics_timer()
    
    def setup_ui(self):
        """设置UI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = ttk.Label(main_frame, text="邮件管理器 - 智能版", font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0, columnspan=4, pady=(0, 10))

        # 配置信息显示
        config_frame = ttk.LabelFrame(main_frame, text="配置信息", padding="10")
        config_frame.grid(row=1, column=0, columnspan=4, pady=(0, 10), sticky=(tk.W, tk.E))

        config_text = f"可用账号: {len(self.config_manager.accounts)} | 最优线程数: {self.config_manager.get_optimal_thread_count()}"
        ttk.Label(config_frame, text=config_text, font=("Arial", 10)).grid(row=0, column=0)

        # 操作控制区域
        control_frame = ttk.LabelFrame(main_frame, text="智能操作控制", padding="10")
        control_frame.grid(row=2, column=0, columnspan=4, pady=(0, 10), sticky=(tk.W, tk.E))

        # 智能操作按钮
        self.start_button = ttk.Button(control_frame, text="🚀 开始智能运行", command=self.start_smart_operation)
        self.start_button.grid(row=0, column=0, padx=(0, 10))

        self.stop_button = ttk.Button(control_frame, text="⏹ 停止运行", command=self.stop_operation, state="disabled")
        self.stop_button.grid(row=0, column=1, padx=(0, 10))

        ttk.Button(control_frame, text="📝 清空日志", command=self.clear_log).grid(row=0, column=2)
        
        # 线程状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="线程状态", padding="10")
        status_frame.grid(row=3, column=0, columnspan=4, pady=(0, 10), sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建滚动区域
        self.status_canvas = tk.Canvas(status_frame, height=150)
        status_scrollbar = ttk.Scrollbar(status_frame, orient="vertical", command=self.status_canvas.yview)
        self.status_scrollable_frame = ttk.Frame(self.status_canvas)
        
        self.status_scrollable_frame.bind("<Configure>", lambda _: self.status_canvas.configure(scrollregion=self.status_canvas.bbox("all")))
        self.status_canvas.create_window((0, 0), window=self.status_scrollable_frame, anchor="nw")
        self.status_canvas.configure(yscrollcommand=status_scrollbar.set)
        
        self.status_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        status_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        main_frame.rowconfigure(4, weight=1)
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = time.strftime('%H:%M:%S')
        self.log_text.insert(tk.END, f"{timestamp} - {message}\n")
        self.log_text.see(tk.END)

    def update_thread_status(self, thread_id: int, status: str):
        """更新线程状态"""
        try:
            status_label = getattr(self, f"status_label_{thread_id}", None)
            if status_label:
                color_map = {
                    "正在初始化": "blue",
                    "正常运行": "green",
                    "已暂停": "orange",
                    "正在停止": "orange",
                    "初始化失败": "red",
                    "运行异常": "red",
                    "已停止": "gray"
                }
                color = color_map.get(status, "black")
                status_label.config(text=status, foreground=color)
        except Exception as e:
            print(f"更新UI状态时出错: {e}")

    def update_statistics_timer(self):
        """定时更新统计信息"""
        try:
            if self.is_running:
                for thread_id, worker in self.worker_threads.items():
                    stats = worker.get_statistics()
                    count_label = getattr(self, f"count_label_{thread_id}", None)
                    if count_label:
                        text = f"总操作:{stats['operation_count']} | 创建:{stats['create_count']} | 删除:{stats['delete_count']}"
                        count_label.config(text=text)
        except Exception as e:
            print(f"更新统计信息时出错: {e}")

        # 每3秒更新一次
        self.root.after(3000, self.update_statistics_timer)

    def create_thread_status_display(self, thread_id: int, account_email: str):
        """创建线程状态显示控件"""
        frame = ttk.Frame(self.status_scrollable_frame)
        frame.grid(row=thread_id-1, column=0, pady=2, sticky=(tk.W, tk.E))

        # 线程信息
        ttk.Label(frame, text=f"智能线程{thread_id}:", width=12).grid(row=0, column=0, padx=(0, 5))
        ttk.Label(frame, text=account_email, width=25).grid(row=0, column=1, padx=(0, 10))

        # 状态标签
        status_label = ttk.Label(frame, text="未启动", width=12, foreground="gray", font=("Arial", 9, "bold"))
        status_label.grid(row=0, column=2, padx=(0, 10))

        # 统计标签
        count_label = ttk.Label(frame, text="总操作:0 | 创建:0 | 删除:0", width=30, font=("Arial", 9))
        count_label.grid(row=0, column=3)

        # 存储控件引用
        setattr(self, f"status_label_{thread_id}", status_label)
        setattr(self, f"count_label_{thread_id}", count_label)

    def update_status_display(self):
        """更新状态显示"""
        # 清除现有显示
        for widget in self.status_scrollable_frame.winfo_children():
            widget.destroy()

        # 为每个可用账号创建状态显示
        available_accounts = self.config_manager.get_available_accounts()
        for i, account_id in enumerate(available_accounts, 1):
            account_info = self.config_manager.get_account_info(account_id)
            if account_info:
                self.create_thread_status_display(i, account_info['email'])

    def start_smart_operation(self):
        """启动智能操作"""
        if self.is_running:
            self.log_message("⚠️ 智能运行已在进行中，请先停止")
            return

        try:
            self.is_running = True
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")

            self.log_message("🚀 开始智能运行...")
            self.log_message("💡 系统将自动分析账号状态并选择最优操作策略")

            # 获取可用账号
            available_accounts = self.config_manager.get_available_accounts()
            optimal_thread_count = self.config_manager.get_optimal_thread_count()

            # 创建并启动智能工作线程
            for i in range(min(len(available_accounts), optimal_thread_count)):
                account_id = available_accounts[i]
                account_info = self.config_manager.get_account_info(account_id)

                if account_info:
                    thread_id = i + 1

                    # 创建智能工作线程
                    worker = SmartWorkerThread(
                        thread_id=thread_id,
                        account_info=account_info,
                        status_callback=self.update_thread_status
                    )

                    # 创建并启动线程
                    thread_obj = threading.Thread(target=worker.run, daemon=True)

                    self.worker_threads[thread_id] = worker
                    self.thread_objects[thread_id] = thread_obj

                    thread_obj.start()
                    self.log_message(f"🤖 智能线程{thread_id}已启动 - {account_info['email']}")

            self.log_message(f"✅ 共启动{len(self.worker_threads)}个智能线程")

        except Exception as e:
            self.log_message(f"❌ 启动智能操作失败: {e}")
            self.is_running = False
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")

    def stop_operation(self):
        """停止智能操作"""
        if not self.is_running:
            self.log_message("ℹ️ 当前没有正在执行的智能操作")
            return

        try:
            # 立即更新UI状态，不等待线程结束
            self.is_running = False
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")

            self.log_message("⏹ 正在停止智能运行...")

            # 发送停止信号给所有线程
            for thread_id, worker in self.worker_threads.items():
                worker.stop()
                self.log_message(f"🛑 智能线程{thread_id}已发送停止信号")

            # 在后台异步等待线程结束，不阻塞UI
            def cleanup_threads():
                """后台清理线程"""
                import time

                cleanup_start = time.time()
                for thread_id, thread_obj in self.thread_objects.items():
                    # 只等待2秒，避免长时间阻塞
                    thread_obj.join(timeout=2)
                    if thread_obj.is_alive():
                        self.log_message(f"⚠️ 智能线程{thread_id}仍在运行，将在后台自动结束")
                    else:
                        self.log_message(f"✅ 智能线程{thread_id}已结束")

                # 清理线程字典
                self.worker_threads.clear()
                self.thread_objects.clear()

                cleanup_time = time.time() - cleanup_start
                self.log_message(f"✅ 智能运行已停止 (清理耗时: {cleanup_time:.1f}秒)")

            # 启动后台清理线程
            import threading
            cleanup_thread = threading.Thread(target=cleanup_threads, daemon=True)
            cleanup_thread.start()

            self.log_message("✅ 停止信号已发送，正在后台清理...")

        except Exception as e:
            self.log_message(f"❌ 停止操作时出错: {e}")

            # 确保按钮状态正确
            self.is_running = False
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")

    def on_closing(self):
        """窗口关闭事件"""
        if self.is_running:
            self.stop_operation()
            self.root.after(1000, self.root.destroy)  # 延迟1秒后关闭
        else:
            self.root.destroy()

    def run(self):
        """运行UI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = EmailManagerUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")


if __name__ == "__main__":
    main()
