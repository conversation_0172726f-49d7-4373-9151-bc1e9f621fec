"""
数据库操作模块
负责邮箱数据存储和已使用邮箱管理
"""

import sqlite3
import threading
import os
from datetime import datetime
from typing import List, Optional, Tuple


class DatabaseManager:
    """数据库管理器"""
    
    # 类级别的数据库锁，所有实例共享
    _db_lock = threading.Lock()
    
    def __init__(self):
        # 将数据库文件固定存储在桌面上
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        self.db_name = os.path.join(desktop_path, "apple_hide_emails.db")
        print(f"数据库路径: {self.db_name}")
        
        self.db_lock = DatabaseManager._db_lock
        self.init_database()
    
    def init_database(self):
        """初始化SQLite数据库"""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                
                # 创建邮箱表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS emails (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        email TEXT NOT NULL UNIQUE,
                        create_time TEXT NOT NULL
                    )
                ''')
                
                # 创建已使用邮箱表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS 已使用 (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        email TEXT NOT NULL UNIQUE,
                        use_time TEXT NOT NULL,
                        remark TEXT
                    )
                ''')
                
                conn.commit()
                conn.close()
                print("数据库初始化成功")
                
        except Exception as e:
            print(f"数据库初始化失败: {e}")
    
    def save_email(self, email: str) -> bool:
        """保存邮箱到数据库"""
        with self.db_lock:
            try:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
                cursor.execute(
                    "INSERT OR IGNORE INTO emails (email, create_time) VALUES (?, ?)",
                    (email, current_time)
                )
                
                success = cursor.rowcount > 0
                if success:
                    print(f"邮箱已保存: {email}")
                    conn.commit()
                else:
                    print(f"邮箱已存在，跳过: {email}")
                
                conn.close()
                return success
                
            except Exception as e:
                print(f"保存邮箱失败: {e}")
                return False
    
    def delete_email(self, email: str, is_complete_delete: bool = False) -> bool:
        """从数据库删除邮箱"""
        with self.db_lock:
            try:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
                if is_complete_delete:
                    # 彻底删除（从已使用表删除）
                    cursor.execute("DELETE FROM 已使用 WHERE email = ?", (email,))
                    remark = "彻底删除"
                else:
                    # 从emails表删除并移到已使用表
                    cursor.execute("DELETE FROM emails WHERE email = ?", (email,))
                    # 检查已使用表是否有remark列
                    cursor.execute("PRAGMA table_info(已使用)")
                    columns = [column[1] for column in cursor.fetchall()]

                    if 'remark' in columns:
                        cursor.execute(
                            "INSERT OR IGNORE INTO 已使用 (email, use_time, remark) VALUES (?, ?, ?)",
                            (email, current_time, "已删除")
                        )
                    else:
                        cursor.execute(
                            "INSERT OR IGNORE INTO 已使用 (email, use_time) VALUES (?, ?)",
                            (email, current_time)
                        )
                    remark = "已删除"
                
                success = cursor.rowcount > 0
                if success:
                    print(f"邮箱{remark}: {email}")
                    conn.commit()
                else:
                    print(f"邮箱不存在，跳过: {email}")
                
                conn.close()
                return success
                
            except Exception as e:
                print(f"删除邮箱失败: {e}")
                return False
    
    def get_used_emails(self) -> List[str]:
        """从数据库获取已使用的邮箱列表"""
        with self.db_lock:
            try:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                cursor.execute("SELECT email FROM 已使用 ORDER BY use_time DESC")
                emails = [row[0] for row in cursor.fetchall()]
                conn.close()
                return emails
            except Exception as e:
                print(f"获取已使用邮箱失败: {e}")
                return []
    
    def get_available_emails(self) -> List[str]:
        """获取可用邮箱列表"""
        with self.db_lock:
            try:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                cursor.execute("SELECT email FROM emails ORDER BY create_time DESC")
                emails = [row[0] for row in cursor.fetchall()]
                conn.close()
                return emails
            except Exception as e:
                print(f"获取可用邮箱失败: {e}")
                return []
    
    def get_email_counts(self) -> Tuple[int, int]:
        """获取邮箱数量统计"""
        with self.db_lock:
            try:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                
                # 获取可用邮箱数量
                cursor.execute("SELECT COUNT(*) FROM emails")
                available_count = cursor.fetchone()[0]
                
                # 获取已使用邮箱数量
                cursor.execute("SELECT COUNT(*) FROM 已使用")
                used_count = cursor.fetchone()[0]
                
                conn.close()
                return available_count, used_count
                
            except Exception as e:
                print(f"获取邮箱统计失败: {e}")
                return 0, 0
    
    def clear_all_emails(self) -> bool:
        """清空所有邮箱数据"""
        with self.db_lock:
            try:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                
                cursor.execute("DELETE FROM emails")
                cursor.execute("DELETE FROM 已使用")
                
                conn.commit()
                conn.close()
                print("所有邮箱数据已清空")
                return True
                
            except Exception as e:
                print(f"清空邮箱数据失败: {e}")
                return False
    
    def email_exists_in_available(self, email: str) -> bool:
        """检查邮箱是否在可用列表中"""
        with self.db_lock:
            try:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                cursor.execute("SELECT 1 FROM emails WHERE email = ?", (email,))
                exists = cursor.fetchone() is not None
                conn.close()
                return exists
            except Exception as e:
                print(f"检查邮箱存在性失败: {e}")
                return False
    
    def move_email_to_used(self, email: str, remark: str = "手动移动") -> bool:
        """将邮箱从可用列表移动到已使用列表"""
        return self.delete_email(email, is_complete_delete=False)

