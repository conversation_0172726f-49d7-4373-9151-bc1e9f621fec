#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
iCloud邮箱管理工具打包脚本
使用PyInstaller将Python项目打包为可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# 项目配置
PROJECT_NAME = "iCloudmail"
MAIN_SCRIPT = "main.py"
ICON_FILE = "icon.ico"  # 如果有图标文件
VERSION = "1.0.0"

# 打包配置
BUILD_DIR = "build"
DIST_DIR = "dist"
SPEC_FILE = f"{PROJECT_NAME}.spec"

def clean_build():
    """清理之前的构建文件"""
    print("🧹 清理构建文件...")
    
    dirs_to_clean = [BUILD_DIR, DIST_DIR, "__pycache__"]
    files_to_clean = [SPEC_FILE]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除目录: {dir_name}")
    
    for file_name in files_to_clean:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"   删除文件: {file_name}")
    
    # 清理Python缓存文件
    for root, dirs, files in os.walk("."):
        for dir_name in dirs[:]:
            if dir_name == "__pycache__":
                shutil.rmtree(os.path.join(root, dir_name))
                dirs.remove(dir_name)
        for file_name in files:
            if file_name.endswith(('.pyc', '.pyo')):
                os.remove(os.path.join(root, file_name))
    
    print("✅ 清理完成")

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")

    # 包名和模块名的映射
    required_packages = {
        "pyinstaller": "PyInstaller",  # 包名: 模块名
        "DrissionPage": "DrissionPage",
        "requests": "requests"
    }

    missing_packages = []

    for package_name, module_name in required_packages.items():
        try:
            __import__(module_name)
            print(f"   ✅ {package_name}")
        except ImportError:
            missing_packages.append(package_name)
            print(f"   ❌ {package_name}")

    if missing_packages:
        print(f"\n❌ 缺少依赖项: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False

    print("✅ 所有依赖项已安装")
    return True

def create_pyinstaller_command():
    """创建PyInstaller命令"""
    print("📦 准备打包命令...")
    
    cmd = [
        "pyinstaller",
        "--onefile",  # 打包成单个文件
        "--windowed",  # Windows下隐藏控制台窗口
        "--clean",  # 清理临时文件
        f"--name={PROJECT_NAME}",  # 指定输出文件名
        f"--distpath={DIST_DIR}",  # 指定输出目录
        f"--workpath={BUILD_DIR}",  # 指定工作目录
    ]
    
    # 添加图标（如果存在）
    if os.path.exists(ICON_FILE):
        cmd.extend([f"--icon={ICON_FILE}"])
        print(f"   添加图标: {ICON_FILE}")
    
    # 添加数据文件
    data_files = [
        ("config.json", "."),  # 配置文件
        ("README.md", "."),    # 说明文件
    ]
    
    for src, dst in data_files:
        if os.path.exists(src):
            cmd.extend([f"--add-data={src};{dst}"])
            print(f"   添加数据文件: {src}")
    
    # 隐藏导入的模块
    hidden_imports = [
        "DrissionPage",
        "requests",
        "json",
        "sqlite3",
        "threading",
        "time",
        "random",
        "string"
    ]
    
    for module in hidden_imports:
        cmd.extend([f"--hidden-import={module}"])
    
    # 排除不需要的模块（移除tkinter，因为程序需要使用）
    excludes = [
        "matplotlib",
        "numpy",
        "pandas",
        "PIL",
        "cv2"
    ]

    for module in excludes:
        cmd.extend([f"--exclude-module={module}"])
    
    # 添加主脚本
    cmd.append(MAIN_SCRIPT)
    
    return cmd

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    # 检查主脚本是否存在
    if not os.path.exists(MAIN_SCRIPT):
        print(f"❌ 主脚本不存在: {MAIN_SCRIPT}")
        return False
    
    # 创建PyInstaller命令
    cmd = create_pyinstaller_command()
    
    print("执行命令:")
    print(" ".join(cmd))
    print()
    
    try:
        # 执行打包命令
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功!")
        
        # 显示输出文件信息
        exe_file = os.path.join(DIST_DIR, f"{PROJECT_NAME}.exe")
        if os.path.exists(exe_file):
            file_size = os.path.getsize(exe_file) / (1024 * 1024)  # MB
            print(f"📁 输出文件: {exe_file}")
            print(f"📏 文件大小: {file_size:.2f} MB")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败!")
        print(f"错误信息: {e}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def create_installer():
    """创建安装包（可选）"""
    print("📦 创建安装包...")
    
    # 这里可以添加创建安装包的逻辑
    # 例如使用NSIS、Inno Setup等工具
    
    print("ℹ️  安装包创建功能待实现")

def main():
    """主函数"""
    print(f"🚀 开始构建 {PROJECT_NAME} v{VERSION}")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists(MAIN_SCRIPT):
        print(f"❌ 请在项目根目录运行此脚本")
        print(f"❌ 未找到主脚本: {MAIN_SCRIPT}")
        sys.exit(1)
    
    try:
        # 1. 清理构建文件
        clean_build()
        print()
        
        # 2. 检查依赖项
        if not check_dependencies():
            sys.exit(1)
        print()
        
        # 3. 构建可执行文件
        if not build_executable():
            sys.exit(1)
        print()
        
        # 4. 创建安装包（可选）
        # create_installer()
        
        print("=" * 50)
        print("🎉 构建完成!")
        print(f"📁 可执行文件位于: {DIST_DIR}/{PROJECT_NAME}.exe")
        
    except KeyboardInterrupt:
        print("\n❌ 构建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 构建过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
