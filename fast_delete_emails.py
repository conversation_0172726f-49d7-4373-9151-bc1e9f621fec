"""
快速删除邮箱脚本
功能：高速删除所有隐藏邮箱地址，采用多线程并发操作
特点：
1. 简化逻辑，专注速度
2. 多线程并发删除
3. 单线程登录，多窗口共享账号
4. 不操作数据库，直接删除
5. 随机选择邮箱删除
"""

import time
import threading
import random
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
from DrissionPage import Chromium, ChromiumOptions
from typing import Optional, List
import requests
import re

# 导入原有的配置管理器
from config import ConfigManager


class FastEmailDeleter:
    """快速邮箱删除器"""

    def __init__(self, thread_id: int, account_info: dict, ui_callback=None):
        self.thread_id = thread_id
        self.account_info = account_info
        self.ui_callback = ui_callback

        # 账号信息
        self.login_email = account_info.get('email', '')
        self.login_password = account_info.get('password', '')
        self.api_url = account_info.get('api_url', '')

        # 目标网址
        self.TARGET_URL = "https://account.apple.com/account/manage/section/privacy"

        # 浏览器相关
        self.page = None
        self.tab = None

        # 线程控制
        self.should_stop = threading.Event()
        self.is_running = False

        # 统计
        self.delete_count = 0

        print(f"[线程{self.thread_id}] 初始化快速删除器 - {self.login_email}")

    def open_browser(self):
        """打开浏览器 - 使用1501开始的端口"""
        port = 1500 + self.thread_id
        print(f"[线程{self.thread_id}] 使用端口: {port}")
        
        co = ChromiumOptions().set_paths(local_port=port)
        self.page = Chromium(addr_or_opts=co)
        
        # 检查是否有已打开的标签页
        for t in self.page.get_tabs():
            if t.title != '新标签页':
                print(f"[线程{self.thread_id}] 监听现有页面: {t.title}")
                self.tab = t
                self.tab.wait.doc_loaded()
                return True
        
        # 使用最新标签页
        self.tab = self.page.latest_tab
        self.tab.get(self.TARGET_URL)
        print(f"[线程{self.thread_id}] 已打开目标页面")
        return True

    def login_if_needed(self) -> bool:
        """如果需要则登录"""
        try:
            current_url = self.tab.url
            
            # 检查是否在登录页面
            if "sign-in" in current_url:
                print(f"[线程{self.thread_id}] 检测到登录页面，开始登录...")
                return self.perform_login()
            
            # 检查是否已在目标页面
            if "privacy" in current_url:
                print(f"[线程{self.thread_id}] 已在目标页面")
                return True
                
            # 尝试导航到目标页面
            self.tab.get(self.TARGET_URL)
            time.sleep(2)
            
            # 再次检查是否需要登录
            if "sign-in" in self.tab.url:
                return self.perform_login()
                
            return True
            
        except Exception as e:
            print(f"[线程{self.thread_id}] 登录检查失败: {e}")
            return False

    def perform_login(self) -> bool:
        """执行登录操作"""
        try:
            # 输入邮箱
            email_input = self.tab.ele('#account_name_text_field', timeout=3)
            if email_input:
                email_input.clear()
                email_input.input(self.login_email)
                print(f"[线程{self.thread_id}] 已输入邮箱")
                time.sleep(0.5)

                # 点击登录按钮
                login_btn = self.tab.ele('#sign-in', timeout=3)
                if login_btn:
                    login_btn.click()
                    time.sleep(1)

            # 输入密码
            password_input = self.tab.ele('#password_text_field', timeout=5)
            if password_input:
                password_input.clear()
                password_input.input(self.login_password)
                print(f"[线程{self.thread_id}] 已输入密码")
                time.sleep(0.5)

                # 点击登录按钮
                login_btn = self.tab.ele('#sign-in', timeout=3)
                if login_btn:
                    login_btn.click()
                    print(f"[线程{self.thread_id}] 点击登录按钮")
                    time.sleep(3)

                    # 检查是否需要验证码
                    if self.handle_verification_if_needed():
                        print(f"[线程{self.thread_id}] 登录完成")
                        return True
                    else:
                        print(f"[线程{self.thread_id}] 验证码处理失败")
                        return False

            return False

        except Exception as e:
            print(f"[线程{self.thread_id}] 登录失败: {e}")
            return False

    def handle_verification_if_needed(self) -> bool:
        """处理验证码（如果需要）"""
        try:
            # 检查是否出现验证码输入框
            verification_input = self.tab.ele('css:.form-security-code-input', timeout=3)
            if verification_input:
                print(f"[线程{self.thread_id}] 检测到验证码页面")

                # 尝试从API获取验证码
                verification_code = self.get_verification_code_from_api()
                if verification_code:
                    verification_input.clear()
                    verification_input.input(verification_code)
                    print(f"[线程{self.thread_id}] 已输入验证码: {verification_code}")
                    time.sleep(2)

                    # 等待验证完成
                    time.sleep(5)
                    return True
                else:
                    print(f"[线程{self.thread_id}] 无法获取验证码")
                    return False
            else:
                # 没有验证码页面，直接返回成功
                return True

        except Exception as e:
            print(f"[线程{self.thread_id}] 验证码处理出错: {e}")
            return False

    def get_verification_code_from_api(self) -> Optional[str]:
        """从API获取验证码"""
        try:
            if not self.api_url:
                print(f"[线程{self.thread_id}] API地址未配置")
                return None

            response = requests.get(self.api_url, timeout=10)
            response.raise_for_status()

            data = response.json()
            print(f"[线程{self.thread_id}] API响应: {data}")

            if data.get('code') == 1 and 'data' in data:
                code_text = data['data'].get('code', '')

                # 使用正则表达式提取验证码（6位数字）
                match = re.search(r'\b(\d{6})\b', code_text)
                if match:
                    verification_code = match.group(1)
                    print(f"[线程{self.thread_id}] 获取验证码成功: {verification_code}")
                    return verification_code
                else:
                    print(f"[线程{self.thread_id}] 无法从文本中提取验证码: {code_text}")
                    return None
            else:
                print(f"[线程{self.thread_id}] API返回错误: {data}")
                return None

        except Exception as e:
            print(f"[线程{self.thread_id}] 获取验证码失败: {e}")
            return None

    def click_hide_email_card(self) -> bool:
        """点击隐藏邮件地址卡片"""
        try:
            # 查找隐藏邮件地址卡片
            hide_card = self.tab.ele("css:h3.card-title", timeout=2)
            if hide_card and "隐藏邮件地址" in hide_card.text:
                hide_card.click()
                print(f"[线程{self.thread_id}] 成功点击隐藏邮件地址卡片")
                time.sleep(1)
                return True
            else:
                print(f"[线程{self.thread_id}] 未找到隐藏邮件地址卡片")
                return False
                
        except Exception as e:
            print(f"[线程{self.thread_id}] 点击隐藏邮件地址卡片失败: {e}")
            return False

    def get_random_email_card(self):
        """随机选择一个邮箱卡片"""
        try:
            # 获取所有邮箱卡片
            cards = self.tab.eles("css:div.card-line", timeout=2)
            if cards:
                # 过滤出包含邮箱地址的卡片（包含@icloud.com）
                valid_cards = []
                for card in cards:
                    if card.text and "@icloud.com" in card.text:
                        valid_cards.append(card)

                if valid_cards:
                    # 随机选择一个有效的邮箱卡片
                    selected_card = random.choice(valid_cards)
                    print(f"[线程{self.thread_id}] 随机选择邮箱卡片 (共{len(valid_cards)}个有效卡片)")
                    print(f"[线程{self.thread_id}] 选中卡片内容: {selected_card.text[:50]}...")
                    return selected_card
                else:
                    print(f"[线程{self.thread_id}] 未找到包含邮箱地址的有效卡片")
                    return None
            else:
                print(f"[线程{self.thread_id}] 未找到邮箱卡片")
                return None

        except Exception as e:
            print(f"[线程{self.thread_id}] 获取邮箱卡片失败: {e}")
            return None

    def delete_email_fast(self) -> bool:
        """快速删除邮箱流程"""
        try:
            # 1. 随机选择邮箱卡片并点击
            email_card = self.get_random_email_card()
            if not email_card:
                return False

            # 尝试点击卡片的左侧区域（避免点击右侧的"来自设置"）
            try:
                # 方法1：尝试点击卡片内的邮箱地址文本
                email_text_element = email_card.ele("css:.text", timeout=1)
                if email_text_element and "@icloud.com" in email_text_element.text:
                    email_text_element.click()
                    print(f"[线程{self.thread_id}] 点击邮箱地址文本: {email_text_element.text}")
                else:
                    # 方法2：点击卡片的左侧区域
                    email_card.click(offset_x=-50)  # 向左偏移50像素点击
                    print(f"[线程{self.thread_id}] 点击邮箱卡片左侧区域")
            except:
                # 方法3：直接点击卡片中心
                email_card.click()
                print(f"[线程{self.thread_id}] 点击邮箱卡片中心")

            time.sleep(0.5)
            
            # 2. 查找并点击删除或停用按钮
            delete_btn = self.tab.ele("tag:button@text():删除地址", timeout=2)
            deactivate_btn = self.tab.ele("tag:button@text():停用电子邮件地址", timeout=2)
            
            if delete_btn:
                delete_btn.click()
                print(f"[线程{self.thread_id}] 点击删除按钮")
                time.sleep(0.5)
                
                # 3. 确认删除
                confirm_btn = self.find_confirm_button("删除")
                if confirm_btn:
                    confirm_btn.click()
                    print(f"[线程{self.thread_id}] 确认删除")
                    self.delete_count += 1
                    time.sleep(0.5)
                    return True
                    
            elif deactivate_btn:
                deactivate_btn.click()
                print(f"[线程{self.thread_id}] 点击停用按钮")
                time.sleep(0.5)
                
                # 3. 确认停用
                confirm_btn = self.find_confirm_button("停用")
                if confirm_btn:
                    confirm_btn.click()
                    print(f"[线程{self.thread_id}] 确认停用")
                    self.delete_count += 1
                    time.sleep(0.5)
                    return True
            
            return False
            
        except Exception as e:
            print(f"[线程{self.thread_id}] 删除邮箱失败: {e}")
            return False

    def find_confirm_button(self, button_text: str):
        """查找确认按钮"""
        try:
            buttons = self.tab.eles("tag:button", timeout=2)
            for btn in buttons:
                if btn.text and btn.text.strip() == button_text and btn.states.is_displayed:
                    return btn
            return None
        except:
            return None

    def close_dialog_if_needed(self):
        """如果需要则关闭对话框"""
        try:
            close_btn = self.tab.ele("css:span.icon.icon-close", timeout=1)
            if close_btn and close_btn.states.is_displayed:
                close_btn.click()
                print(f"[线程{self.thread_id}] 关闭对话框")
                time.sleep(0.5)
        except:
            pass

    def refresh_page(self):
        """刷新页面"""
        try:
            self.tab.get(self.TARGET_URL)
            time.sleep(2)
            print(f"[线程{self.thread_id}] 页面已刷新")
        except Exception as e:
            print(f"[线程{self.thread_id}] 刷新页面失败: {e}")

    def run_delete_loop(self):
        """运行删除循环"""
        self.is_running = True
        
        try:
            # 初始化浏览器
            if not self.open_browser():
                print(f"[线程{self.thread_id}] 浏览器初始化失败")
                return
            
            # 登录
            if not self.login_if_needed():
                print(f"[线程{self.thread_id}] 登录失败")
                return
            
            print(f"[线程{self.thread_id}] 开始快速删除循环...")
            
            # 主删除循环
            while not self.should_stop.is_set():
                try:
                    # 1. 点击隐藏邮件地址卡片
                    if not self.click_hide_email_card():
                        # 如果失败，刷新页面重试
                        self.refresh_page()
                        continue
                    
                    # 2. 执行删除操作
                    if self.delete_email_fast():
                        # 删除成功，更新UI
                        if self.ui_callback:
                            self.ui_callback(self.thread_id, self.delete_count)
                        
                        # 关闭对话框
                        self.close_dialog_if_needed()
                        
                        # 短暂延迟后继续
                        time.sleep(0.2)
                    else:
                        # 删除失败，关闭对话框并重试
                        self.close_dialog_if_needed()
                        time.sleep(0.5)
                        
                except Exception as e:
                    print(f"[线程{self.thread_id}] 循环中出错: {e}")
                    self.close_dialog_if_needed()
                    time.sleep(1)
            
        except Exception as e:
            print(f"[线程{self.thread_id}] 运行出错: {e}")
        finally:
            self.is_running = False
            print(f"[线程{self.thread_id}] 删除循环结束，总计删除: {self.delete_count}")

    def stop(self):
        """停止删除"""
        self.should_stop.set()
        print(f"[线程{self.thread_id}] 收到停止信号")


class FastDeleteUI:
    """快速删除UI界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("快速删除邮箱工具")
        self.root.geometry("800x600")

        # 配置管理器
        self.config_manager = ConfigManager()

        # 验证配置
        is_valid, errors = self.config_manager.validate_config()
        if not is_valid:
            messagebox.showerror("配置错误", "\n".join(errors))
            self.root.destroy()
            return

        # 线程管理
        self.deleters = {}
        self.threads = {}
        self.is_running = False

        self.setup_ui()

    def setup_ui(self):
        """设置UI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 账号配置区域
        config_frame = ttk.LabelFrame(main_frame, text="账号配置", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)

        # 显示配置的账号信息
        available_accounts = self.config_manager.get_available_accounts()
        if available_accounts:
            account_info = self.config_manager.get_account_info(available_accounts[0])
            account_text = f"已加载 {len(available_accounts)} 个账号，当前使用: {account_info['email']}"
        else:
            account_text = "未找到有效账号配置"

        ttk.Label(config_frame, text="配置状态:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.account_label = ttk.Label(config_frame, text=account_text)
        self.account_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))

        # 显示API状态
        if available_accounts:
            api_status = "✅ 接码API已配置" if account_info.get('api_url') else "❌ 接码API未配置"
        else:
            api_status = "❌ 无可用账号"

        ttk.Label(config_frame, text="API状态:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.api_label = ttk.Label(config_frame, text=api_status)
        self.api_label.grid(row=1, column=1, sticky=tk.W, padx=(0, 10), pady=(5, 0))

        # 线程配置区域
        thread_frame = ttk.LabelFrame(main_frame, text="线程配置", padding="10")
        thread_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 设置线程数量 - 支持多线程使用同一账号
        default_threads = 3  # 默认3个线程
        max_threads = 10     # 最大10个线程

        ttk.Label(thread_frame, text="线程数量:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.thread_count_var = tk.StringVar(value=str(default_threads))
        thread_spinbox = ttk.Spinbox(thread_frame, from_=1, to=max_threads, textvariable=self.thread_count_var, width=10)
        thread_spinbox.grid(row=0, column=1, sticky=tk.W)

        ttk.Label(thread_frame, text="(所有线程使用同一账号)").grid(row=0, column=2, sticky=tk.W, padx=(5, 0))

        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        self.start_button = ttk.Button(control_frame, text="开始快速删除", command=self.start_deletion)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(control_frame, text="停止删除", command=self.stop_deletion, state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_button = ttk.Button(control_frame, text="清空日志", command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT)

        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="线程状态", padding="10")
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(0, weight=1)

        # 创建状态表格
        columns = ('线程ID', '状态', '删除数量')
        self.status_tree = ttk.Treeview(status_frame, columns=columns, show='headings', height=6)

        for col in columns:
            self.status_tree.heading(col, text=col)
            self.status_tree.column(col, width=100)

        self.status_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 滚动条
        status_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_tree.yview)
        status_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.status_tree.configure(yscrollcommand=status_scrollbar.set)

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def update_thread_status(self, thread_id: int, delete_count: int):
        """更新线程状态"""
        # 查找并更新对应的行
        for item in self.status_tree.get_children():
            values = self.status_tree.item(item, 'values')
            if values and int(values[0]) == thread_id:
                self.status_tree.item(item, values=(thread_id, "运行中", delete_count))
                break

    def start_deletion(self):
        """开始删除操作"""
        try:
            # 获取可用账号
            available_accounts = self.config_manager.get_available_accounts()
            if not available_accounts:
                self.log_message("❌ 没有可用的账号配置")
                return

            # 获取线程数量
            thread_count = int(self.thread_count_var.get())

            self.log_message(f"🚀 开始快速删除，使用{thread_count}个线程")
            self.log_message(f"📋 可用账号数量: {len(available_accounts)}")

            # 清空状态表格
            for item in self.status_tree.get_children():
                self.status_tree.delete(item)

            # 使用第一个可用账号
            account_id = available_accounts[0]
            account_info = self.config_manager.get_account_info(account_id)

            if not account_info:
                self.log_message("❌ 无法获取账号信息")
                return

            self.log_message(f"📧 所有线程将使用账号: {account_info['email']}")

            # 创建并启动删除线程
            for i in range(thread_count):
                thread_id = i + 1

                # 所有线程都使用同一个账号信息
                deleter = FastEmailDeleter(
                    thread_id=thread_id,
                    account_info=account_info,
                    ui_callback=self.update_thread_status
                )

                # 创建线程
                thread = threading.Thread(target=deleter.run_delete_loop, daemon=True)

                # 保存引用
                self.deleters[thread_id] = deleter
                self.threads[thread_id] = thread

                # 添加到状态表格
                self.status_tree.insert('', 'end', values=(thread_id, "启动中", 0))

                # 启动线程
                thread.start()
                self.log_message(f"✅ 线程{thread_id}已启动 - 端口{1500 + thread_id}")

            # 更新UI状态
            self.is_running = True
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")

        except Exception as e:
            self.log_message(f"❌ 启动失败: {e}")

    def stop_deletion(self):
        """停止删除操作"""
        try:
            self.log_message("🛑 正在停止所有线程...")

            # 发送停止信号
            for deleter in self.deleters.values():
                deleter.stop()

            # 等待线程结束
            for thread in self.threads.values():
                if thread.is_alive():
                    thread.join(timeout=2)

            # 清理
            self.deleters.clear()
            self.threads.clear()

            # 更新状态表格
            for item in self.status_tree.get_children():
                values = self.status_tree.item(item, 'values')
                if values:
                    self.status_tree.item(item, values=(values[0], "已停止", values[2]))

            # 更新UI状态
            self.is_running = False
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")

            self.log_message("✅ 所有线程已停止")

        except Exception as e:
            self.log_message(f"❌ 停止失败: {e}")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")

    def on_closing(self):
        """窗口关闭事件"""
        if self.is_running:
            self.stop_deletion()
            self.root.after(1000, self.root.destroy)
        else:
            self.root.destroy()

    def run(self):
        """运行UI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


if __name__ == '__main__':
    try:
        print("=== 快速删除邮箱工具 ===")
        print("启动UI界面...")
        ui = FastDeleteUI()
        ui.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
