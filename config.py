"""
配置管理模块
负责.env文件解析、账号信息管理和线程数量智能计算
合并了 load_accounts.py 的功能，提供统一的配置管理接口
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, env_file='.env'):
        self.env_file = env_file
        self.env_vars = {}
        self.accounts = {}
        self.total_accounts = 0
        self.max_threads = 0
        self.operation_interval = 2
        
        self.load_config()
    
    def load_env_file(self) -> Dict[str, str]:
        """加载.env文件 - 优先从桌面读取"""
        env_vars = {}
        
        # 获取桌面路径
        desktop_path = Path.home() / "Desktop"
        desktop_env_file = desktop_path / self.env_file
        
        # 优先尝试从桌面读取
        if desktop_env_file.exists():
            file_to_read = str(desktop_env_file)
            print(f"从桌面加载配置文件: {file_to_read}")
        elif os.path.exists(self.env_file):
            # 如果桌面没有，尝试当前目录
            file_to_read = self.env_file
            print(f"从当前目录加载配置文件: {file_to_read}")
        else:
            print(f"警告: 未找到配置文件！")
            print(f"请将 {self.env_file} 文件放在桌面或程序目录下")
            return env_vars
        
        try:
            with open(file_to_read, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    # 跳过空行和注释行（包括以#开头的行）
                    if not line or line.startswith('#'):
                        continue
                    # 跳过被注释的行（行首有#的情况）
                    if line.startswith('# '):
                        continue
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        # 只有当值不为空时才添加
                        if value:
                            env_vars[key] = value
        except Exception as e:
            print(f"读取 {file_to_read} 文件时出错: {e}")
        
        return env_vars
    
    def parse_accounts(self) -> Dict[int, Dict[str, str]]:
        """解析账号信息"""
        accounts = {}
        
        # 查找所有账号
        account_ids = set()
        for key in self.env_vars.keys():
            if key.startswith('ACCOUNT_') and '_EMAIL' in key:
                account_id = int(key.split('_')[1])
                account_ids.add(account_id)
        
        # 解析每个账号的完整信息
        for account_id in sorted(account_ids):
            email_key = f"ACCOUNT_{account_id}_EMAIL"
            password_key = f"ACCOUNT_{account_id}_PASSWORD"
            api_key = f"ACCOUNT_{account_id}_API_URL"
            
            email = self.env_vars.get(email_key, "")
            password = self.env_vars.get(password_key, "")
            api_url = self.env_vars.get(api_key, "")
            
            # 只有当邮箱、密码和API都存在时才认为是有效账号
            if email and password and api_url:
                accounts[account_id] = {
                    'email': email,
                    'password': password,
                    'api_url': api_url
                }
                print(f"加载账号{account_id}: {email}")
            else:
                print(f"账号{account_id}信息不完整，跳过")
        
        return accounts
    
    def load_config(self):
        """加载完整配置"""
        print("正在加载配置...")
        
        # 加载环境变量
        self.env_vars = self.load_env_file()
        
        # 解析账号信息
        self.accounts = self.parse_accounts()
        
        # 获取总账号数量
        self.total_accounts = int(self.env_vars.get('TOTAL_ACCOUNTS', len(self.accounts)))
        
        # 获取最大线程数（默认等于总账号数）
        self.max_threads = int(self.env_vars.get('MAX_THREADS', self.total_accounts))
        
        # 获取操作间隔时间
        self.operation_interval = int(self.env_vars.get('OPERATION_INTERVAL', 2))
        
        print(f"配置加载完成:")
        print(f"  - 有效账号数量: {len(self.accounts)}")
        print(f"  - 配置的总账号数: {self.total_accounts}")
        print(f"  - 最大线程数: {self.max_threads}")
        print(f"  - 操作间隔: {self.operation_interval}秒")
    
    def get_account_info(self, account_id: int) -> Optional[Dict[str, str]]:
        """获取指定账号信息"""
        return self.accounts.get(account_id)
    
    def get_available_accounts(self) -> List[int]:
        """获取所有可用账号ID列表"""
        return list(self.accounts.keys())
    
    def get_optimal_thread_count(self) -> int:
        """获取最优线程数量"""
        available_accounts = len(self.accounts)
        return min(available_accounts, self.max_threads)
    
    def validate_config(self) -> Tuple[bool, List[str]]:
        """验证配置有效性"""
        errors = []
        
        if not self.accounts:
            errors.append("没有找到有效的账号配置")
        
        if self.total_accounts <= 0:
            errors.append("TOTAL_ACCOUNTS必须大于0")
        
        if len(self.accounts) < self.total_accounts:
            errors.append(f"配置的账号数量({len(self.accounts)})少于TOTAL_ACCOUNTS({self.total_accounts})")
        
        return len(errors) == 0, errors


# ========== 兼容性函数 (来自 load_accounts.py) ==========

def load_env_file(file_path: str = '.env') -> Dict[str, str]:
    """
    加载 .env 文件 (兼容函数)

    Args:
        file_path: .env 文件路径

    Returns:
        包含环境变量的字典
    """
    env_vars = {}

    if not os.path.exists(file_path):
        print(f"警告: {file_path} 文件不存在")
        return env_vars

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()

                # 跳过空行和注释行
                if not line or line.startswith('#'):
                    continue

                # 解析键值对
                if '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip()
                else:
                    print(f"警告: 第{line_num}行格式不正确: {line}")

    except Exception as e:
        print(f"读取 {file_path} 文件时出错: {e}")

    return env_vars

def get_account_info_legacy(account_id: int, env_vars: Dict[str, str]) -> Tuple[str, str, str]:
    """
    获取指定账号的信息 (兼容函数)

    Args:
        account_id: 账号ID (1-11)
        env_vars: 环境变量字典

    Returns:
        (邮箱, 密码, API地址) 的元组
    """
    email_key = f"ACCOUNT_{account_id}_EMAIL"
    password_key = f"ACCOUNT_{account_id}_PASSWORD"
    api_key = f"ACCOUNT_{account_id}_API_URL"

    email = env_vars.get(email_key, "")
    password = env_vars.get(password_key, "")
    api_url = env_vars.get(api_key, "")

    return email, password, api_url

def get_all_accounts(env_vars: Dict[str, str]) -> List[Dict[str, str]]:
    """
    获取所有账号信息 (兼容函数)

    Args:
        env_vars: 环境变量字典

    Returns:
        包含所有账号信息的列表
    """
    accounts = []
    total_accounts = int(env_vars.get('TOTAL_ACCOUNTS', 0))

    for i in range(1, total_accounts + 1):
        email, password, api_url = get_account_info_legacy(i, env_vars)

        if email and password and api_url:
            accounts.append({
                'id': i,
                'email': email,
                'password': password,
                'api_url': api_url
            })

    return accounts


# ========== 主函数和测试 ==========

def main():
    """主函数 - 演示配置管理器的使用"""
    print("=== 配置管理器演示 ===\n")

    try:
        # 使用新的ConfigManager
        print("1. 使用 ConfigManager:")
        config = ConfigManager()

        print(f"   可用账号数: {len(config.accounts)}")
        print(f"   最优线程数: {config.get_optimal_thread_count()}")

        available_accounts = config.get_available_accounts()
        for account_id in available_accounts[:3]:  # 只显示前3个
            account_info = config.get_account_info(account_id)
            print(f"   账号{account_id}: {account_info['email']}")

        print("\n" + "="*50)

        # 使用兼容性函数
        print("2. 使用兼容性函数 (load_accounts.py 风格):")
        env_vars = load_env_file()

        if env_vars:
            accounts = get_all_accounts(env_vars)
            print(f"   共加载了 {len(accounts)} 个账号")

            for account in accounts[:3]:  # 只显示前3个
                print(f"   账号{account['id']}: {account['email']}")

        print("\n✅ 两种方式功能一致，推荐使用 ConfigManager")

    except Exception as e:
        print(f"❌ 演示过程出错: {e}")


if __name__ == '__main__':
    main()
