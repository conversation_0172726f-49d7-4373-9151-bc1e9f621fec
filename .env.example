# Apple账号配置文件示例
# 请复制此文件为 .env 并填入真实的账号信息
# 格式：ACCOUNT_X_EMAIL=邮箱地址
#      ACCOUNT_X_PASSWORD=密码
#      ACCOUNT_X_API_URL=验证码API地址

# 账号1 - 必填
ACCOUNT_1_EMAIL=<EMAIL>
ACCOUNT_1_PASSWORD=your_password1
ACCOUNT_1_API_URL=http://your_api_server/api/record?token=your_token1

# 账号2 - 可选
ACCOUNT_2_EMAIL=<EMAIL>
ACCOUNT_2_PASSWORD=your_password2
ACCOUNT_2_API_URL=http://your_api_server/api/record?token=your_token2

# 账号3 - 可选
ACCOUNT_3_EMAIL=<EMAIL>
ACCOUNT_3_PASSWORD=your_password3
ACCOUNT_3_API_URL=http://your_api_server/api/record?token=your_token3

# 账号4 - 可选
ACCOUNT_4_EMAIL=<EMAIL>
ACCOUNT_4_PASSWORD=your_password4
ACCOUNT_4_API_URL=http://your_api_server/api/record?token=your_token4

# 账号5 - 可选
ACCOUNT_5_EMAIL=<EMAIL>
ACCOUNT_5_PASSWORD=your_password5
ACCOUNT_5_API_URL=http://your_api_server/api/record?token=your_token5

# 总账号数量 - 根据实际配置的账号数量调整
TOTAL_ACCOUNTS=5

# 可选配置项
# 最大线程数（默认等于账号数量）
MAX_THREADS=5

# 操作间隔时间（秒，默认2秒）
OPERATION_INTERVAL=2

# 配置说明：
# 1. 每个账号需要配置三个参数：EMAIL（邮箱）、PASSWORD（密码）、API_URL（接码API地址）
# 2. 账号编号从1开始，连续编号
# 3. TOTAL_ACCOUNTS 设置为实际配置的账号数量
# 4. 程序会自动加载所有有效的账号配置
# 5. 多线程会循环使用配置的账号，避免冲突
# 6. 接码API用于自动处理登录时的验证码

# 注意事项：
# - 请确保所有账号信息准确无误
# - 接码API地址必须有效且能正常返回验证码
# - 不要在配置文件中包含中文字符（除注释外）
# - 保护好此文件，避免账号信息泄露
